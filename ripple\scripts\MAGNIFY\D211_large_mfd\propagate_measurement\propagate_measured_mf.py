from ripple.field import <PERSON><PERSON><PERSON><PERSON>
from ripple.monitors import FieldMonitor
from ripple.coupling_optimizer import CouplingScene
from ripple.utils.sim_helpers import run_sim, plot_monitor_from_file
from ripple.field_distribution import ImportedImageFieldDistribution, GaussianDistribution

from ripple.utils.export_data import plot_field_grid_far_field
import matplotlib.pyplot as plt

# Imported source from MFD measurement
# source_field = ImportedImageFieldDistribution(r'0_0.0.tiff', pixel_size_um=0.15)
# source_field = TransverseField(distribution=source_field, z0=0, refractive_index=1., wavelength=1.55)
source_field = TransverseField(distribution=GaussianDistribution(x0=0, y0=0, w0x=1/2, w0y=1/2),
                                       z0=0, refractive_index=1, wavelength=1.55)
# source_field.rotate_field_xy(theta_x_deg=-4)

monitor_xz = FieldMonitor(monitor_type='xz', position=0, saving_path=None, record_beam_radius_per_z=True)
# monitor_xy = FieldMonitor(monitor_type='xy', position=5, saving_path=None, record_beam_radius_per_z=True)
# monitor_xy2 = FieldMonitor(monitor_type='xy', position=10, saving_path=None, record_beam_radius_per_z=True)

scene = CouplingScene(input_field=source_field, target_field=None,
                      background_material_index=1.,
                      optical_structures=[],
                      sim_size_xy=[50, 50], sim_z_end=20,
                      wavelength_sampling_xy=20,
                      material_wavelength_sampling_z=20,
                      background_wavelength_sampling_z=10,
                      monitors=[],
                      # monitors=[monitor_xz, monitor_xy, monitor_xy2],
                      solver='wpm',
                      boundary_condition='ABC', boundary_parameters=None)
# source_field.view()
# plt.show()
#
# run_sim(scene, optimize_structure=False, opt_pars=None, opt_method='Nelder-Mead', cal_coupling=False,
#         name='optimized', sim_result_dir='./results', opt_log_dir=None, show_plot=True, draw_4dsigma_contour=False,
#         draw_contour=False)

plot_monitor_from_file(file_path=r'C:\Users\<USER>\Documents\PycharmProject\ripple\scripts\MAGNIFY\D211_large_mfd\propagate_measurement\results\3_monitor_xy_optimized.pkl',
                       scene=scene, draw_4dsigma_contour=False, draw_contour=False)
plt.show()