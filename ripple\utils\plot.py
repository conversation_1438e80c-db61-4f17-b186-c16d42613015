import numpy as np
import matplotlib.pyplot as plt


def _set_axes_equal_3d(ax):
    """
    Make axes of 3D plot have equal scale so that spheres appear as spheres,
    cubes as cubes, etc.

    Input
      ax: a matplotlib axis, e.g., as output from plt.gca().
    """

    x_limits = ax.get_xlim3d()
    y_limits = ax.get_ylim3d()
    z_limits = ax.get_zlim3d()

    x_range = abs(x_limits[1] - x_limits[0])
    x_middle = np.mean(x_limits)
    y_range = abs(y_limits[1] - y_limits[0])
    y_middle = np.mean(y_limits)
    z_range = abs(z_limits[1] - z_limits[0])
    z_middle = np.mean(z_limits)

    # The plot bounding box is a sphere in the sense of the infinity
    # norm, hence I call half the max range the plot radius.
    plot_radius = 0.5*max([x_range, y_range, z_range])

    ax.set_xlim3d([x_middle - plot_radius, x_middle + plot_radius])
    ax.set_ylim3d([y_middle - plot_radius, y_middle + plot_radius])
    ax.set_zlim3d([z_middle - plot_radius, z_middle + plot_radius])


def _set_axes_equal_2d(ax):
    x_limits = ax.get_xlim()
    y_limits = ax.get_ylim()

    x_range = abs(x_limits[1] - x_limits[0])
    x_middle = np.mean(x_limits)
    y_range = abs(y_limits[1] - y_limits[0])
    y_middle = np.mean(y_limits)

    # The plot bounding box is a sphere in the sense of the infinity
    # norm, hence I call half the max range the plot radius.
    plot_radius = 0.5*max([x_range, y_range])

    ax.set_xlim([x_middle - plot_radius, x_middle + plot_radius])
    ax.set_ylim([y_middle - plot_radius, y_middle + plot_radius])


def set_axes_equal(ax):
    if ax.name == '3d':
        _set_axes_equal_3d(ax)
    else:
        _set_axes_equal_2d(ax)


def plot_polygon(ax, poly, **kwargs):
    from matplotlib.path import Path
    from matplotlib.patches import PathPatch
    from matplotlib.collections import PatchCollection

    path = Path.make_compound_path(
        Path(np.asarray(poly.exterior.coords)[:, :2]),
        *[Path(np.asarray(ring.coords)[:, :2]) for ring in poly.interiors])

    patch = PathPatch(path, **kwargs)
    collection = PatchCollection([patch], **kwargs)

    ax.add_collection(collection, autolim=True)
    ax.autoscale_view()
    return collection


def draw_contour(ax, x, y, Z, contour: (int, float), annotate=True):
    max_val = np.max(Z)
    CS = ax.contour(x, y, Z, [contour * max_val], colors='white', linestyles='--')
    def fmt(x):
        if np.isclose(x, np.exp(-1), atol=0.05):
            return '1/e'
        elif np.isclose(x, np.exp(-2), atol=0.05):
            return r'$1/e^2$'
        else:
            return f"{x:.2f}"
    if annotate:
        fmt_dict = {lvl: fmt(lvl / max_val) for lvl in CS.levels}
        ax.clabel(CS, CS.levels, inline=True, fmt=fmt_dict, fontsize=10)


def get_norm(norm_option, vmin=None, vmax=None):
    import matplotlib.colors as colors

    if norm_option == 'PowerNorm':
        norm = colors.PowerNorm(gamma=0.5, vmin=vmin, vmax=vmax)
    elif norm_option == 'Normalize':
        norm = colors.Normalize(vmin=vmin, vmax=vmax)
    elif norm_option == 'Customized':
        def _forward(x):
            # return np.exp(-10 * x)
            return np.where(x < np.amax(x), 1 - np.sqrt(1 - x), np.sqrt(x - 0.5))
        def _inverse(x):
            # return -1/10 * np.log(x)
            return np.log(x)
        norm = colors.FuncNorm((_forward, _inverse), vmin=vmin, vmax=vmax)
        return norm, _forward, _inverse
    else:
        raise NotImplemented(f'norm_option {norm_option} is not implemented.')

    return norm


def colormesh(ax, Z, x=None, y=None, cmap='hot', norm_option='PowerNorm', vmin=None, vmax=None):
    vmin = np.amin(Z) if vmin is None else vmin
    vmax = np.amax(Z) if vmax is None else vmax

    if norm_option == 'Customized':
        norm, forward, backward = get_norm(norm_option=norm_option, vmin=vmin, vmax=vmax)
    else:
        norm = get_norm(norm_option=norm_option, vmin=vmin, vmax=vmax)

    if x is not None and y is not None:
        im = ax.pcolormesh(x, y, Z.T, cmap=cmap, shading='auto', norm=norm)
    else:
        im = ax.pcolormesh(Z.T, cmap=cmap, shading='auto', norm=norm)
    ax.set_aspect('equal', adjustable='box')

    # divider = make_axes_locatable(ax)
    # cax = divider.append_axes("right", size="5%", pad=0.05)
    # cbar = plt.colorbar(im, cax=cax)

    cbar = plt.colorbar(im, ax=ax, shrink=0.7, pad=0.01, aspect=10, anchor=(0., 0.5))
    # cbar = plt.colorbar(im, ax=ax, shrink=0.95, pad=0.01, aspect=10, anchor=(0., 0.5))
    # cbar.ax.set_yticks([0.0, 0.5, 1.0])
    # cbar.ax.tick_params(direction='in')

    if norm_option == 'Customized':
        from ripple.utils.interpolations import find_x_for_y
        linear_ticks = np.array([0., 0.4, 1.0])
        nonlinear_ticks = norm(linear_ticks)
        ticks = find_x_for_y(x=linear_ticks, y=nonlinear_ticks, y_value=linear_ticks)
        cbar.set_ticks(ticks)  # Map tick positions to the non-linear scale
        cbar.set_ticklabels([f'{t:.1f}' for t in linear_ticks])  # Set equal tick labels

        cbar.ax.tick_params(labelsize=plt.rcParams['font.size'] * 0.8)  # Adjust label size

    if norm_option == 'Customized':
        cbar.ax.invert_yaxis()

    return im


def get_ax_labels(mode):
    mode = mode.lower()
    cut_dict = {'xy': 'z', 'xz': 'y', 'yz': 'x'}
    assert mode in cut_dict, f"Mode: {mode} is not supported"
    ax_annot = mode[::-1] if 'z' in mode else mode
    return ax_annot[0], ax_annot[1], cut_dict[mode]
