import os
import matplotlib.pyplot as plt
import numpy as np

from ripple.field import Transverse<PERSON>ield
from ripple.field_distribution import GaussianDistribution
from ripple.monitors import FieldMonitor
from ripple.structures import Lens
from ripple.coupling_optimizer import CouplingScene
from ripple.utils.sim_helpers import run_sim, plot_monitor_from_dir

focus_distance = 600
coupling_angle_deg = 3
center_wl = 1.55
global_shift = 0

source_field = TransverseField(distribution=GaussianDistribution(x0=global_shift, y0=0, w0x=10.4 / 2, w0y=10.4 / 2),
                               z0=0, refractive_index=1.53, wavelength=center_wl)
# target_field = TransverseField(distribution=GaussianDistribution(x0=0, y0=0, w0x=50 / 2, w0y=50 / 2),
#                                z0=focus_distance, refractive_index=1,
#                                wavelength=source_field.wavelength)
monitor_xz = FieldMonitor(monitor_type='xz', position=0, saving_path=None,
                          record_power_per_z=True, record_beam_radius_per_z=True)
# Coupling efficiency: 0.5989914247773895
lens = Lens(first_surface_type='ConoidalAsphericSurface',
            first_surface_parameters={'x0': 3.815286470060454, 'y0': 0, 'z0': 26.042940490223145,
                                      'rho_x': -0.05991782575031679, 'kappa_x': -0.24981543831547243,
                                      'rho_y': -0.06236763105312454, 'kappa_y': -0.2640557159132405},
            second_surface_type='ConoidalAsphericSurface',
            second_surface_parameters={'x0': 10.720467751496656, 'y0': 0, 'z0': 252.56150047179125,
                                       'rho_x': -0.011628600592971907, 'kappa_x': -0.5062642587496687,
                                       'rho_y': -0.011403606554221787, 'kappa_y': -0.5087836726638675},
            max_radius=200,
            refractive_index=1.53,
            priority=1)

target_pos = (focus_distance - lens._second_surface_parameters['z0']) * np.sin(np.deg2rad(coupling_angle_deg)) + global_shift
print(f"{target_pos = }")   # -26.588141777664717
target_field = TransverseField(distribution=GaussianDistribution(x0=target_pos,
                                                                 y0=0, w0x=25, w0y=25),
                               z0=focus_distance, refractive_index=1, wavelength=center_wl)
target_field.rotate_field_xy(theta_y_deg=coupling_angle_deg)

lens.translate(tx=global_shift, ty=0, tz=0, attach_to=source_field, attach_after_translation=False)

scene = CouplingScene(input_field=source_field, target_field=target_field,
                      background_material_index=1.,
                      optical_structures=[lens],
                      sim_size_xy=[300, 200],
                      wavelength_sampling_xy=[4, 4],
                      material_wavelength_sampling_z=2,
                      background_wavelength_sampling_z=2,
                      monitors=[monitor_xz],
                      boundary_condition='ABC', boundary_parameters=None)


def run(save_dir=None):
    run_sim(scene, optimize_structure=False, opt_pars=None, opt_method='Nelder-Mead', cal_coupling=True,
            name='optimized', sim_result_dir=save_dir, opt_log_dir=None, show_plot=True)


def optimize(save_dir=None):
    opt_pars = {lens.name: {'Surface': {'first_surface': #['x0'],
                                            ['x0', 'z0', 'kappa_x', 'rho_x', 'kappa_y', 'rho_y'],
                                        'second_surface': #['x0'],
                                            ['x0', 'kappa_x', 'rho_x', 'kappa_y', 'rho_y']
                                        }}}
    run_sim(scene, optimize_structure=True, opt_pars=opt_pars, opt_method='Nelder-Mead', cal_coupling=True,
            name='optimized', sim_result_dir=save_dir, opt_log_dir=save_dir, show_plot=True)


if __name__ == '__main__':
    # scene.preview(mode='xz', position=0)
    # plt.show()

    # save_dir = 'data'
    save_dir = None

    optimize(save_dir=save_dir)
    # run(save_dir=save_dir)

    # plot_monitor_from_dir(save_dir, scene=scene)
    # plt.show()
