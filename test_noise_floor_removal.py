#!/usr/bin/env python3
"""
Test script for noise floor removal functionality in ImportedImageFieldDistribution.

This script demonstrates different methods for removing noise floor from TIFF images.
"""

import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import os

# Import the class we want to test
from ripple.field_distribution import ImportedImageFieldDistribution


def create_noisy_tiff_image(filename="noisy_field.tiff", width=80, height=60, noise_floor=50):
    """
    Create a TIFF image with a known noise floor for testing.
    
    Parameters:
    -----------
    filename : str
        Output filename for the TIFF image
    width : int
        Image width in pixels
    height : int
        Image height in pixels
    noise_floor : float
        Constant noise floor value to add to the signal
        
    Returns:
    --------
    str
        Path to the created TIFF file
    """
    # Create coordinate grids
    x = np.linspace(-2, 2, width)
    y = np.linspace(-1.5, 1.5, height)
    X, Y = np.meshgrid(x, y)
    
    # Create a Gaussian beam pattern
    sigma = 0.5
    gaussian = np.exp(-(X**2 + Y**2) / (2 * sigma**2))
    
    # Add some random noise
    random_noise = np.random.normal(0, 0.05, gaussian.shape)
    
    # Create the signal with noise floor
    signal = gaussian + random_noise
    
    # Add constant noise floor and scale to 8-bit range
    signal_with_floor = signal + noise_floor/255.0  # Normalize noise floor
    
    # Convert to 8-bit image (0-255)
    image_data = (signal_with_floor * 255).astype(np.uint8)
    
    # Save as TIFF
    img = Image.fromarray(image_data, mode='L')
    img.save(filename)
    
    print(f"Created noisy TIFF image: {filename}")
    print(f"  Image size: {width} x {height} pixels")
    print(f"  Noise floor: {noise_floor} (8-bit units)")
    print(f"  Actual min value: {np.min(image_data)}")
    print(f"  Actual max value: {np.max(image_data)}")
    
    return filename


def test_noise_floor_removal():
    """Test different noise floor removal methods."""
    
    print("=== Noise Floor Removal Testing ===\n")
    
    # Create a test image with known noise floor
    tiff_file = create_noisy_tiff_image(noise_floor=30)  # 30/255 ≈ 0.12 normalized
    
    try:
        # Create the field distribution instance
        field_dist = ImportedImageFieldDistribution()
        
        # Test different noise floor removal methods
        methods = [
            ('No removal', {'remove_noise_floor': False}),
            ('Min subtraction', {'remove_noise_floor': True, 'noise_floor_method': 'min_subtraction'}),
            ('1st percentile', {'remove_noise_floor': True, 'noise_floor_method': 'percentile', 'noise_percentile': 1.0}),
            ('5th percentile', {'remove_noise_floor': True, 'noise_floor_method': 'percentile', 'noise_percentile': 5.0}),
            ('Manual (value=30)', {'remove_noise_floor': True, 'noise_floor_method': 'manual', 'noise_percentile': 30.0}),
        ]
        
        results = []
        
        print("\nTesting different noise floor removal methods:")
        print("-" * 60)
        
        for method_name, kwargs in methods:
            print(f"\n{method_name}:")
            
            # Load the image with the specified method
            x_grid, y_grid, E_field = field_dist.import_field_from_file(
                file_path=tiff_file, 
                pixel_size_um=1.0,
                **kwargs
            )
            
            # Store results for visualization
            results.append((method_name, x_grid, y_grid, E_field))
            
            # Print statistics
            print(f"  E-field range: [{E_field.min():.4f}, {E_field.max():.4f}]")
            print(f"  Mean value: {E_field.mean():.4f}")
            print(f"  Standard deviation: {E_field.std():.4f}")
        
        # Create visualization
        print("\nCreating comparison visualization...")
        create_noise_floor_comparison_plot(results)
        
    except Exception as e:
        print(f"Error during testing: {e}")
        raise
    
    finally:
        # Clean up
        if os.path.exists(tiff_file):
            os.remove(tiff_file)
            print(f"\nCleaned up: {tiff_file}")


def create_noise_floor_comparison_plot(results):
    """Create a comparison plot showing different noise floor removal methods."""
    
    n_methods = len(results)
    fig, axes = plt.subplots(2, n_methods, figsize=(4*n_methods, 8))
    
    if n_methods == 1:
        axes = axes.reshape(2, 1)
    
    for i, (method_name, x_grid, y_grid, E_field) in enumerate(results):
        # Top row: 2D field distribution
        ax1 = axes[0, i]
        im1 = ax1.pcolormesh(x_grid, y_grid, E_field, cmap='viridis', shading='nearest')
        ax1.set_xlabel('x (μm)')
        ax1.set_ylabel('y (μm)')
        ax1.set_title(f'{method_name}\nRange: [{E_field.min():.3f}, {E_field.max():.3f}]')
        ax1.set_aspect('equal')
        plt.colorbar(im1, ax=ax1, label='Normalized E-field')
        
        # Bottom row: histogram of values
        ax2 = axes[1, i]
        ax2.hist(E_field.flatten(), bins=50, alpha=0.7, density=True)
        ax2.set_xlabel('Normalized E-field')
        ax2.set_ylabel('Probability density')
        ax2.set_title(f'Value distribution\nMin: {E_field.min():.4f}')
        ax2.grid(True, alpha=0.3)
        ax2.axvline(E_field.min(), color='red', linestyle='--', label=f'Min: {E_field.min():.4f}')
        ax2.axvline(E_field.max(), color='green', linestyle='--', label=f'Max: {E_field.max():.4f}')
        ax2.legend()
    
    plt.tight_layout()
    plt.savefig('noise_floor_removal_comparison.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print("Comparison plot saved as 'noise_floor_removal_comparison.png'")


def demonstrate_percentile_method():
    """Demonstrate the percentile method with different percentile values."""
    
    print("\n=== Percentile Method Demonstration ===\n")
    
    # Create a test image with more complex noise
    tiff_file = create_noisy_tiff_image(filename="complex_noise.tiff", noise_floor=25)
    
    try:
        field_dist = ImportedImageFieldDistribution()
        
        # Test different percentile values
        percentiles = [0.5, 1.0, 2.0, 5.0, 10.0]
        
        print("Testing different percentile values:")
        print("-" * 40)
        
        results = []
        
        for percentile in percentiles:
            print(f"\nPercentile: {percentile}%")
            
            x_grid, y_grid, E_field = field_dist.import_field_from_file(
                file_path=tiff_file, 
                pixel_size_um=1.0,
                remove_noise_floor=True,
                noise_floor_method='percentile',
                noise_percentile=percentile
            )
            
            results.append((f'{percentile}%', x_grid, y_grid, E_field))
            
            print(f"  E-field range: [{E_field.min():.4f}, {E_field.max():.4f}]")
            print(f"  Non-zero pixels: {np.count_nonzero(E_field)} / {E_field.size}")
        
        # Create visualization for percentile comparison
        print("\nCreating percentile comparison visualization...")
        create_percentile_comparison_plot(results)
        
    except Exception as e:
        print(f"Error during percentile demonstration: {e}")
        raise
    
    finally:
        # Clean up
        if os.path.exists(tiff_file):
            os.remove(tiff_file)
            print(f"\nCleaned up: {tiff_file}")


def create_percentile_comparison_plot(results):
    """Create a comparison plot for different percentile values."""
    
    n_percentiles = len(results)
    fig, axes = plt.subplots(1, n_percentiles, figsize=(4*n_percentiles, 4))
    
    if n_percentiles == 1:
        axes = [axes]
    
    for i, (percentile_name, x_grid, y_grid, E_field) in enumerate(results):
        ax = axes[i]
        im = ax.pcolormesh(x_grid, y_grid, E_field, cmap='viridis', shading='nearest')
        ax.set_xlabel('x (μm)')
        ax.set_ylabel('y (μm)')
        ax.set_title(f'Percentile: {percentile_name}\nMin: {E_field.min():.4f}')
        ax.set_aspect('equal')
        plt.colorbar(im, ax=ax, label='Normalized E-field')
    
    plt.tight_layout()
    plt.savefig('percentile_comparison.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print("Percentile comparison plot saved as 'percentile_comparison.png'")


if __name__ == "__main__":
    test_noise_floor_removal()
    demonstrate_percentile_method()
