import os
import sys
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

from ripple.utils.io import list_files_in_dir
from ripple.utils.plot import get_ax_labels
from ripple.monitors import FieldMonitor
from ripple.coupling_optimizer import CouplingScene

# FIXME: this is a mess


def run_sim(scene, optimize_structure=False, opt_pars=None, opt_method='Nelder-Mead', cal_coupling=False, name=None,
            sim_result_dir=None, opt_log_dir=None, show_plot=True, **kwargs):

    name = datetime.now().strftime('%Y-%m-%d_%H-%M-%S') if name is None else name
    if optimize_structure:
        file_stamp = f"{opt_method}_{name}"
    else:
        file_stamp = f"{name}"

    # Optimization
    if optimize_structure:
        assert opt_pars is not None, 'Please provide optimization parameters'
        scene.optimize_structures(parameters_to_optimize=opt_pars, acceptable_coupling_efficiency=0.99,
                                  method=opt_method,  record_process=True, file_stamp=file_stamp,
                                  saving_dir=opt_log_dir)
    coupling = scene.run_simulation(calculate_coupling=cal_coupling)

    if show_plot:
        plot_monitors(scene, file_dir=sim_result_dir, file_stamp=file_stamp, **kwargs)
    return coupling


def plot_monitors(scene, file_dir=None, file_stamp='', field_type='E', show_plots=True,
                  normalize_along_z=False, draw_4dsigma_contour=True, **kwargs):
    for i, monitor in enumerate(scene.monitors):
        file_name = f"{i}_monitor_{monitor.type}_{file_stamp}.pkl"
        if file_dir is not None:
            monitor.export(path=os.path.join(file_dir, file_name))
        if show_plots:
            _view_formatted_monitor(monitor, scene, field_type=field_type, normalize_along_z=normalize_along_z,
                                    draw_4dsigma_contour=draw_4dsigma_contour, **kwargs)
    if show_plots:
        # plt.legend(fontsize=14)
        plt.show()


def plot_monitor_from_file(file_path, scene=None, **kwargs):
    monitor = FieldMonitor.load(path=file_path)
    _view_formatted_monitor(monitor, scene=scene, **kwargs)


def plot_monitor_from_dir(dir_path, scene):
    pkl_files = list_files_in_dir(dir_path=dir_path, suffixes='.pkl')
    monitors = [filename for filename in pkl_files if 'monitor' in filename.lower()]
    if len(monitors) == 0:
        sys.exit('No monitors found in {}'.format(dir_path))
    for monitor in monitors:
        plot_monitor_from_file(file_path=os.path.join(dir_path, monitor), scene=scene)


def _view_formatted_monitor(monitor, scene=None, field_type='E', normalize_along_z=False,
                            draw_4dsigma_contour=False, contour_level=None, draw_contour=True, **kwargs):
    if monitor.type == 'xy':
        if draw_contour:
            if field_type.split('_')[0] == 'E':
                contour_level = 1 / np.e
            elif field_type == 'intensity':
                contour_level = 1 / np.e ** 2
        else:
            contour_level = None
    axes = monitor.view(plot_property='E', plot_op='abs', direction=['forward', 'reflected'][0], # TODO: make this accessible
                        cmap='hot',  # inferno looks nice
                        contour=contour_level, contour_annot=True, scale_func=None,
                        norm_option='Normalize',  # Normalize, PowerNorm, Customized
                        normalize_along_z=normalize_along_z,
                        draw_4dsigma_contour=draw_4dsigma_contour,
                        # scale=(lambda x: np.exp(-30 * x)) if monitor.type != 'xy' else 'linear'
                        # font_pars={'family': 'arial', 'size': 7}, fig_scale=0.34
                        )
    annotate_input = np.abs(monitor.position - scene.input_field.z0) < 3 if monitor.type == 'xy' else True
    annotate_target = not annotate_input if monitor.type == 'xy' else True
    if scene is not None:
        _annotate_scene(axes=axes, scene=scene, mode=monitor.type, annotate_input_field=annotate_input,
                        annotate_output_field=annotate_target)
    x_lim = axes[0].get_xlim()
    for ax in axes[1:]:
        ax.set_xlim(x_lim)

    if monitor.type == 'xy' and scene.target_field is not None:
        c_x, c_y, wx, wy, phi = monitor.cal_beam_size()
        print(f"MFD differences from target: delta_x_mfd = {2*(wx - scene.target_field.w0x):.3f} um, "
              f"delta_y_mfd = {2*(wy - scene.target_field.w0y):.3f} um")


def _annotate_scene(axes, scene, mode, annotate_input_field=True, annotate_output_field=True):
    assert type(scene) is CouplingScene
    x_annot, y_annot, cut = get_ax_labels(mode=mode)
    scene.draw_sim_box(ax=axes[0], x_label=x_annot, y_label=y_annot)
    scene.draw_fields(ax=axes[0], x_label=x_annot, y_label=y_annot,
                      show_input=annotate_input_field, show_target=annotate_output_field)
    for ax in axes[1:]:
        ax.axvline(x=scene.input_field.z0, color='tab:red', linestyle='--', alpha=0.6)
        if getattr(scene, 'target_field', None):
            ax.axvline(x=scene.target_field.z0, color='tab:green', linestyle='--', alpha=0.6)
        if 'radius' in ax.get_ylabel().lower():
            scene.annotate_target_beam_radius(ax)
